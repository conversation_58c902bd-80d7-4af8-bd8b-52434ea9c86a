import os
# Set environment variables to avoid TensorFlow conflicts
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress TensorFlow warnings
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'  # Reduce transformers verbosity

from modelscope import AutoModelForCausalLM, AutoTokenizer
from transformers import TextStreamer
import sys
import time
import torch
import json
import pandas as pd
from datetime import datetime
import re
from typing import List, Dict, Any

model_path = "Qwen3-4B"  # 本地模型路径

class ShipRAGDatabase:
    """船舶信息RAG数据库"""

    def __init__(self):
        self.data = []
        self.df = None

    def load_json_files(self, file_paths: List[str]):
        """加载JSON数据文件"""
        print("正在加载数据文件...")
        all_data = []

        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    if 'data' in json_data and isinstance(json_data['data'], list):
                        all_data.extend(json_data['data'])
                        print(f"成功加载 {file_path}: {len(json_data['data'])} 条记录")
                    else:
                        print(f"警告: {file_path} 格式不正确")
            except Exception as e:
                print(f"加载 {file_path} 时出错: {e}")

        self.data = all_data
        self.df = pd.DataFrame(all_data)

        # 数据预处理
        if not self.df.empty:
            # 转换时间格式
            self.df['tgsj'] = pd.to_datetime(self.df['tgsj'], errors='coerce')
            # 填充空值
            self.df['cmch'] = self.df['cmch'].fillna('未知船名')
            self.df['aissbh'] = self.df['aissbh'].fillna('未知AIS')

        print(f"数据加载完成！总计 {len(self.data)} 条船舶记录")

    def search_ships(self, query: str) -> List[Dict[str, Any]]:
        """搜索船舶信息"""
        if self.df is None or self.df.empty:
            return []

        results = []
        query_lower = query.lower()

        # 检查是否包含数字（可能是AIS设备号）
        ais_numbers = re.findall(r'\d+', query)

        # 检查是否包含日期
        date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', query)

        # 检查是否包含长ID（过闸ID）
        long_id_matches = re.findall(r'[A-F0-9]{20,}', query.upper())

        # 提取可能的船名关键词 - 改进逻辑
        ship_name_keywords = []

        # 首先尝试整个查询作为船名
        if not ais_numbers and not date_matches and not long_id_matches:
            # 移除常见的查询词汇
            clean_query = query
            for remove_word in ['查找', '搜索', '船名', '包含', '的', '船舶', 'ais', '设备号', '时间', '日期', '过闸', 'id', '是多少', '什么', '哪个']:
                clean_query = clean_query.replace(remove_word, '')
            clean_query = clean_query.strip()

            if clean_query:
                ship_name_keywords.append(clean_query)

        # 然后按单词分割
        for word in query.split():
            if len(word) > 1 and not word.isdigit() and word not in ['查找', '搜索', '船名', '包含', '的', '船舶', 'ais', '设备号', '时间', '日期', '过闸', 'id', '是多少', '什么', '哪个']:
                ship_name_keywords.append(word)

        # 1. 按AIS设备号搜索
        if ais_numbers:
            for ais_num in ais_numbers:
                mask = self.df['aissbh'].astype(str).str.contains(ais_num, na=False)
                results.extend(self.df[mask].to_dict('records'))

        # 2. 按日期搜索
        if date_matches:
            for date_str in date_matches:
                try:
                    target_date = pd.to_datetime(date_str).date()
                    mask = self.df['tgsj'].dt.date == target_date
                    results.extend(self.df[mask].to_dict('records'))
                except:
                    pass

        # 3. 按过闸ID搜索
        if long_id_matches:
            for gcdid in long_id_matches:
                mask = self.df['gcdid'].str.contains(gcdid, case=False, na=False)
                results.extend(self.df[mask].to_dict('records'))

        # 4. 按船名关键词搜索
        if ship_name_keywords:
            for keyword in ship_name_keywords:
                mask = self.df['cmch'].str.contains(keyword, case=False, na=False)
                results.extend(self.df[mask].to_dict('records'))

        # 5. 如果没有找到特定模式，进行通用搜索
        if not results:
            # 直接在船名中搜索整个查询字符串
            mask = self.df['cmch'].str.contains(query, case=False, na=False)
            results.extend(self.df[mask].to_dict('records'))

            # 如果还是没找到，按单词搜索
            if not results:
                search_terms = [word for word in query.split() if len(word) > 1]
                for term in search_terms:
                    for col in ['cmch', 'aissbh', 'cbid', 'gcdid']:
                        if col in self.df.columns:
                            mask = self.df[col].astype(str).str.contains(term, case=False, na=False)
                            results.extend(self.df[mask].to_dict('records'))

        # 去重
        seen = set()
        unique_results = []
        for item in results:
            if item['cbid'] not in seen:
                seen.add(item['cbid'])
                unique_results.append(item)

        return unique_results[:10]  # 限制返回前10条结果

    def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        if self.df is None or self.df.empty:
            return {}

        stats = {
            "总记录数": len(self.df),
            "有船名记录数": len(self.df[self.df['cmch'] != '未知船名']),
            "有AIS设备号记录数": len(self.df[self.df['aissbh'] != '未知AIS']),
            "时间范围": {
                "最早": self.df['tgsj'].min().strftime('%Y-%m-%d %H:%M:%S') if pd.notna(self.df['tgsj'].min()) else "未知",
                "最晚": self.df['tgsj'].max().strftime('%Y-%m-%d %H:%M:%S') if pd.notna(self.df['tgsj'].max()) else "未知"
            },
            "唯一过闸ID数": self.df['gcdid'].nunique()
        }
        return stats

# 初始化RAG数据库
rag_db = ShipRAGDatabase()

# load the tokenizer and the model
print("正在加载模型，请稍候...")
tokenizer = AutoTokenizer.from_pretrained(
    model_path,
    trust_remote_code=True
)
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype="auto",
    device_map="auto",
    trust_remote_code=True
)
print("模型加载完成！")

def format_ship_data(ships: List[Dict[str, Any]]) -> str:
    """格式化船舶数据为可读文本"""
    if not ships:
        return "未找到相关船舶信息。"

    formatted_text = f"找到 {len(ships)} 条船舶记录：\n\n"
    for i, ship in enumerate(ships, 1):
        formatted_text += f"{i}. 船舶信息：\n"
        formatted_text += f"   - 船舶ID: {ship.get('cbid', '未知')}\n"
        formatted_text += f"   - 船名: {ship.get('cmch', '未知')}\n"
        formatted_text += f"   - AIS设备号: {ship.get('aissbh', '未知')}\n"
        formatted_text += f"   - 过闸ID: {ship.get('gcdid', '未知')}\n"
        formatted_text += f"   - 通过时间: {ship.get('tgsj', '未知')}\n\n"

    return formatted_text

def chat_with_model(enable_thinking=True):
    # 首先加载数据
    data_files = ["57d55cae476818831e41b8a7edbe3007.json", "dbe6a7821cce479ab6152a446c88b3c4.json"]
    rag_db.load_json_files(data_files)

    messages = []
    print("\n=== 船舶信息RAG智能问答系统 ===")
    print("选择模式：")
    print("1. 开启思考模式")
    print("2. 关闭思考模式")
    print("\n特殊命令：")
    print("- 输入 'quit' 退出对话")
    print("- 输入 'switch' 切换模式")
    print("- 输入 'stats' 查看数据统计")
    print("- 输入 'help' 查看帮助信息")

    # 显示数据统计
    stats = rag_db.get_statistics()
    if stats:
        print(f"\n数据库状态：已加载 {stats['总记录数']} 条船舶记录")
        print(f"时间范围：{stats['时间范围']['最早']} 至 {stats['时间范围']['最晚']}")

    # 初始模式选择
    while True:
        mode = input("\n请选择模式 (1/2): ")
        if mode in ['1', '2']:
            enable_thinking = (mode == '1')
            print(f"\n当前模式：{'开启' if enable_thinking else '关闭'}思考模式")
            break
        print("无效输入，请输入 1 或 2")
    
    # 主对话循环
    while True:
        user_input = input("\n你: ")
        if user_input.lower() == 'quit':
            print("\n再见！")
            break
        elif user_input.lower() == 'switch':
            enable_thinking = not enable_thinking
            print(f"\n已切换到：{'开启' if enable_thinking else '关闭'}思考模式")
            continue
        elif user_input.lower() == 'stats':
            stats = rag_db.get_statistics()
            print("\n=== 数据库统计信息 ===")
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"{key}:")
                    for sub_key, sub_value in value.items():
                        print(f"  {sub_key}: {sub_value}")
                else:
                    print(f"{key}: {value}")
            continue
        elif user_input.lower() == 'help':
            print("\n=== 帮助信息 ===")
            print("您可以询问以下类型的问题：")
            print('1. 按船名搜索："查找船名包含江淮的船舶"')
            print('2. 按AIS搜索："查找AIS设备号3957319的船舶"')
            print('3. 按时间搜索："查找2024-09-09的船舶记录"')
            print('4. 按过闸ID搜索："查找过闸ID为360648E5E42F2ACCCFE22DC261405的记录"')
            print('5. 统计查询："有多少条船舶记录？"')
            print('6. 一般问题：关于船舶数据的任何问题')
            continue

        # RAG检索
        search_results = rag_db.search_ships(user_input)
        context = ""

        if search_results:
            context = f"\n相关船舶数据：\n{format_ship_data(search_results)}"

        # 构建增强的用户输入
        enhanced_input = user_input
        if context:
            enhanced_input += f"\n\n基于以下数据回答：{context}"

        messages.append({"role": "user", "content": enhanced_input})
        
        # prepare the model input
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=enable_thinking
        )
        model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

        # 创建流式输出器
        streamer = TextStreamer(tokenizer, skip_special_tokens=True)
        
        print()  # 添加空行
        # 使用streamer进行流式生成
        output_ids = model.generate(
            **model_inputs,
            max_new_tokens=32768,
            streamer=streamer
        )
        
        # 获取生成的完整文本用于保存到对话历史
        output_ids = output_ids[0][len(model_inputs.input_ids[0]):].tolist()
        try:
            # rindex finding 151668 (</think>)
            index = len(output_ids) - output_ids[::-1].index(151668)
        except ValueError:
            index = 0
        
        content = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")
        # 将AI的回复添加到对话历史
        messages.append({"role": "assistant", "content": content})
        print()  # 添加空行

def check_data_files():
    """检查数据文件是否存在"""
    data_files = ["57d55cae476818831e41b8a7edbe3007.json", "dbe6a7821cce479ab6152a446c88b3c4.json"]
    missing_files = []

    for file_path in data_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print("错误：以下数据文件不存在：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保数据文件在当前目录下。")
        return False

    return True

if __name__ == "__main__":
    print("=== 船舶信息RAG智能问答系统 ===")
    print("正在初始化系统...")

    # 检查数据文件
    if not check_data_files():
        print("系统初始化失败，程序退出。")
        sys.exit(1)

    try:
        chat_with_model()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断，再见！")
    except Exception as e:
        print(f"\n程序运行出错：{e}")
        print("请检查模型路径和数据文件是否正确。")
import os
# Set environment variables to avoid TensorFlow conflicts
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress TensorFlow warnings
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'  # Reduce transformers verbosity

from modelscope import AutoModelForCausalLM, AutoTokenizer
from transformers import TextStreamer
import sys
import time
import torch
import json
import pandas as pd
from datetime import datetime
import re
from typing import List, Dict, Any

model_path = "Qwen3-4B"  # 本地模型路径

class ShipRAGDatabase:
    """船舶信息RAG数据库"""

    def __init__(self):
        self.data = []
        self.df = None

    def load_json_files(self, file_paths: List[str]):
        """加载JSON数据文件"""
        print("正在加载数据文件...")
        all_data = []

        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    if 'data' in json_data and isinstance(json_data['data'], list):
                        all_data.extend(json_data['data'])
                        print(f"成功加载 {file_path}: {len(json_data['data'])} 条记录")
                    else:
                        print(f"警告: {file_path} 格式不正确")
            except Exception as e:
                print(f"加载 {file_path} 时出错: {e}")

        self.data = all_data
        self.df = pd.DataFrame(all_data)

        # 数据预处理
        if not self.df.empty:
            # 转换时间格式
            self.df['tgsj'] = pd.to_datetime(self.df['tgsj'], errors='coerce')
            # 填充空值
            self.df['cmch'] = self.df['cmch'].fillna('未知船名')
            self.df['aissbh'] = self.df['aissbh'].fillna('未知AIS')

        print(f"数据加载完成！总计 {len(self.data)} 条船舶记录")

    def analyze_query(self, query: str) -> Dict[str, Any]:
        """AI分析查询内容，识别搜索主体和意图"""
        analysis = {
            'ship_names': [],
            'ais_numbers': [],
            'dates': [],
            'gcd_ids': [],
            'query_type': 'unknown',
            'requested_fields': []
        }

        # 1. 识别查询类型和请求的字段
        if any(word in query for word in ['时间', '什么时候', '几点', '日期']):
            analysis['requested_fields'].append('tgsj')
            analysis['query_type'] = 'time_query'

        if any(word in query for word in ['ais', 'AIS', 'aissbh', '设备号']):
            analysis['requested_fields'].append('aissbh')
            if analysis['query_type'] == 'unknown':
                analysis['query_type'] = 'ais_query'

        if any(word in query for word in ['过闸', 'gcdid', 'ID', 'id']):
            analysis['requested_fields'].append('gcdid')
            if analysis['query_type'] == 'unknown':
                analysis['query_type'] = 'gcdid_query'

        # 2. 智能提取船名 - 使用多种模式匹配
        ship_name_patterns = [
            # 匹配 "浙德清货2158" 这样的模式
            r'([京津冀鲁豫晋蒙辽吉黑沪苏浙皖闽赣湘鄂桂琼川贵云藏陕甘青宁新渝港澳台][^的是多少什么哪个？?\s]*?货[^的是多少什么哪个？?\s]*?\d+)',
            # 匹配 "江淮货12" 这样的模式
            r'([a-zA-Z]*[京津冀鲁豫晋蒙辽吉黑沪苏浙皖闽赣湘鄂桂琼川贵云藏陕甘青宁新渝港澳台\u4e00-\u9fff]+[^的是多少什么哪个？?\s]*?\d+)',
            # 匹配包含货、船、艇、舶的词
            r'(\w*[货船艇舶]\w*\d+)',
        ]

        for pattern in ship_name_patterns:
            matches = re.findall(pattern, query)
            for match in matches:
                if len(match) > 2 and match not in analysis['ship_names']:
                    analysis['ship_names'].append(match)

        # 3. 提取其他标识符
        analysis['ais_numbers'] = re.findall(r'\b\d{6,8}\b', query)
        analysis['dates'] = re.findall(r'(\d{4}-\d{2}-\d{2})', query)
        analysis['gcd_ids'] = re.findall(r'[A-F0-9]{20,}', query.upper())

        return analysis

    def search_ships(self, query: str) -> List[Dict[str, Any]]:
        """基于AI分析结果进行精准搜索"""
        if self.df is None or self.df.empty:
            return []

        # 先让AI分析查询
        analysis = self.analyze_query(query)
        results = []

        # 1. 优先按船名搜索（最精准）
        if analysis['ship_names']:
            for ship_name in analysis['ship_names']:
                mask = self.df['cmch'].str.contains(ship_name, case=False, na=False)
                found = self.df[mask].to_dict('records')
                results.extend(found)

        # 2. 按AIS设备号搜索
        if analysis['ais_numbers']:
            for ais_num in analysis['ais_numbers']:
                mask = self.df['aissbh'].astype(str).str.contains(ais_num, na=False)
                found = self.df[mask].to_dict('records')
                results.extend(found)

        # 3. 按日期搜索
        if analysis['dates']:
            for date_str in analysis['dates']:
                try:
                    target_date = pd.to_datetime(date_str).date()
                    mask = self.df['tgsj'].dt.date == target_date
                    found = self.df[mask].to_dict('records')
                    results.extend(found)
                except:
                    pass

        # 4. 按过闸ID搜索
        if analysis['gcd_ids']:
            for gcdid in analysis['gcd_ids']:
                mask = self.df['gcdid'].str.contains(gcdid, case=False, na=False)
                found = self.df[mask].to_dict('records')
                results.extend(found)

        # 5. 如果AI没有识别出明确的搜索目标，进行兜底搜索
        if not results and not analysis['ship_names'] and not analysis['ais_numbers'] and not analysis['dates'] and not analysis['gcd_ids']:
            # 直接在船名中搜索整个查询字符串
            mask = self.df['cmch'].str.contains(query, case=False, na=False)
            results.extend(self.df[mask].to_dict('records'))

            # 如果还是没找到，按单词搜索
            if not results:
                search_terms = [word for word in query.split() if len(word) > 1]
                for term in search_terms:
                    for col in ['cmch', 'aissbh', 'cbid', 'gcdid']:
                        if col in self.df.columns:
                            mask = self.df[col].astype(str).str.contains(term, case=False, na=False)
                            results.extend(self.df[mask].to_dict('records'))

        # 去重 - 使用多个字段组合来确保不丢失有效记录
        seen = set()
        unique_results = []
        for item in results:
            # 使用cbid+时间的组合来去重，避免丢失同一船舶的不同时间记录
            key = (item.get('cbid', ''), item.get('tgsj', ''))
            if key not in seen:
                seen.add(key)
                unique_results.append(item)

        return unique_results[:10]  # 限制返回前10条结果

    def get_statistics(self) -> Dict[str, Any]:
        """获取数据统计信息"""
        if self.df is None or self.df.empty:
            return {}

        stats = {
            "总记录数": len(self.df),
            "有船名记录数": len(self.df[self.df['cmch'] != '未知船名']),
            "有AIS设备号记录数": len(self.df[self.df['aissbh'] != '未知AIS']),
            "时间范围": {
                "最早": self.df['tgsj'].min().strftime('%Y-%m-%d %H:%M:%S') if pd.notna(self.df['tgsj'].min()) else "未知",
                "最晚": self.df['tgsj'].max().strftime('%Y-%m-%d %H:%M:%S') if pd.notna(self.df['tgsj'].max()) else "未知"
            },
            "唯一过闸ID数": self.df['gcdid'].nunique()
        }
        return stats

# 初始化RAG数据库
rag_db = ShipRAGDatabase()

# load the tokenizer and the model
print("正在加载模型，请稍候...")
tokenizer = AutoTokenizer.from_pretrained(
    model_path,
    trust_remote_code=True
)
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype="auto",
    device_map="auto",
    trust_remote_code=True
)
print("模型加载完成！")

def format_ship_data(ships: List[Dict[str, Any]]) -> str:
    """格式化船舶数据为可读文本"""
    if not ships:
        return "未找到相关船舶信息。"

    formatted_text = f"找到 {len(ships)} 条船舶记录：\n\n"
    for i, ship in enumerate(ships, 1):
        formatted_text += f"{i}. 船舶信息：\n"
        formatted_text += f"   - 船舶ID: {ship.get('cbid', '未知')}\n"
        formatted_text += f"   - 船名: {ship.get('cmch', '未知')}\n"
        formatted_text += f"   - AIS设备号: {ship.get('aissbh', '未知')}\n"
        formatted_text += f"   - 过闸ID: {ship.get('gcdid', '未知')}\n"
        formatted_text += f"   - 通过时间: {ship.get('tgsj', '未知')}\n\n"

    return formatted_text

def chat_with_model(enable_thinking=True):
    # 首先加载数据
    data_files = ["57d55cae476818831e41b8a7edbe3007.json", "dbe6a7821cce479ab6152a446c88b3c4.json"]
    rag_db.load_json_files(data_files)

    messages = []
    print("\n=== 船舶信息RAG智能问答系统 ===")
    print("选择模式：")
    print("1. 开启思考模式")
    print("2. 关闭思考模式")
    print("\n特殊命令：")
    print("- 输入 'quit' 退出对话")
    print("- 输入 'switch' 切换模式")
    print("- 输入 'stats' 查看数据统计")
    print("- 输入 'help' 查看帮助信息")

    # 显示数据统计
    stats = rag_db.get_statistics()
    if stats:
        print(f"\n数据库状态：已加载 {stats['总记录数']} 条船舶记录")
        print(f"时间范围：{stats['时间范围']['最早']} 至 {stats['时间范围']['最晚']}")

    # 初始模式选择
    while True:
        mode = input("\n请选择模式 (1/2): ")
        if mode in ['1', '2']:
            enable_thinking = (mode == '1')
            print(f"\n当前模式：{'开启' if enable_thinking else '关闭'}思考模式")
            break
        print("无效输入，请输入 1 或 2")
    
    # 主对话循环
    while True:
        user_input = input("\n你: ")
        if user_input.lower() == 'quit':
            print("\n再见！")
            break
        elif user_input.lower() == 'switch':
            enable_thinking = not enable_thinking
            print(f"\n已切换到：{'开启' if enable_thinking else '关闭'}思考模式")
            continue
        elif user_input.lower() == 'stats':
            stats = rag_db.get_statistics()
            print("\n=== 数据库统计信息 ===")
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"{key}:")
                    for sub_key, sub_value in value.items():
                        print(f"  {sub_key}: {sub_value}")
                else:
                    print(f"{key}: {value}")
            continue
        elif user_input.lower() == 'help':
            print("\n=== 帮助信息 ===")
            print("您可以询问以下类型的问题：")
            print('1. 按船名搜索："查找船名包含江淮的船舶"')
            print('2. 按AIS搜索："查找AIS设备号3957319的船舶"')
            print('3. 按时间搜索："查找2024-09-09的船舶记录"')
            print('4. 按过闸ID搜索："查找过闸ID为360648E5E42F2ACCCFE22DC261405的记录"')
            print('5. 统计查询："有多少条船舶记录？"')
            print('6. 一般问题：关于船舶数据的任何问题')
            continue

        # RAG检索
        search_results = rag_db.search_ships(user_input)
        context = ""

        if search_results:
            context = f"\n相关船舶数据：\n{format_ship_data(search_results)}"

        # 如果找到相关船舶信息，将其融入到用户查询中，但不显示给用户
        if context and search_results:
            # 提取关键信息，构建隐式上下文
            ship_context = ""
            for ship in search_results:
                ship_name = ship.get('cmch', '未知')
                ais_num = ship.get('aissbh', '未知')
                time_info = ship.get('tgsj', '未知时间')
                gcdid = ship.get('gcdid', '未知')
                cbid = ship.get('cbid', '未知')

                ship_context += f"船舶{ship_name}的AIS设备号是{ais_num}，通过时间是{time_info}，过闸ID是{gcdid}，船舶ID是{cbid}。"

            # 构建包含船舶信息的查询，但以隐式方式
            enhanced_input = f"根据已知信息：{ship_context}\n\n回答问题：{user_input}\n\n请直接回答，不要提及信息来源。"
        else:
            enhanced_input = user_input

        messages.append({"role": "user", "content": enhanced_input})
        
        # prepare the model input
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=enable_thinking
        )
        model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

        # 创建流式输出器
        streamer = TextStreamer(tokenizer, skip_special_tokens=True)
        
        print()  # 添加空行
        # 使用streamer进行流式生成
        output_ids = model.generate(
            **model_inputs,
            max_new_tokens=32768,
            streamer=streamer
        )
        
        # 获取生成的完整文本用于保存到对话历史
        output_ids = output_ids[0][len(model_inputs.input_ids[0]):].tolist()
        try:
            # rindex finding 151668 (</think>)
            index = len(output_ids) - output_ids[::-1].index(151668)
        except ValueError:
            index = 0
        
        content = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")
        # 将AI的回复添加到对话历史
        messages.append({"role": "assistant", "content": content})
        print()  # 添加空行

def check_data_files():
    """检查数据文件是否存在"""
    data_files = ["57d55cae476818831e41b8a7edbe3007.json", "dbe6a7821cce479ab6152a446c88b3c4.json"]
    missing_files = []

    for file_path in data_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print("错误：以下数据文件不存在：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保数据文件在当前目录下。")
        return False

    return True

if __name__ == "__main__":
    print("=== 船舶信息RAG智能问答系统 ===")
    print("正在初始化系统...")

    # 检查数据文件
    if not check_data_files():
        print("系统初始化失败，程序退出。")
        sys.exit(1)

    try:
        chat_with_model()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断，再见！")
    except Exception as e:
        print(f"\n程序运行出错：{e}")
        print("请检查模型路径和数据文件是否正确。")
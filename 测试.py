import os
# Set environment variables to avoid TensorFlow conflicts
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress TensorFlow warnings
os.environ['TRANSFORMERS_VERBOSITY'] = 'error'  # Reduce transformers verbosity

from modelscope import AutoModelForCausalLM, AutoTokenizer
from transformers import TextStreamer
import sys
import time
import torch

model_path = "Qwen3-4B"  # 本地模型路径

# load the tokenizer and the model
print("正在加载模型，请稍候...")
tokenizer = AutoTokenizer.from_pretrained(
    model_path,
    trust_remote_code=True
)
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype="auto",
    device_map="auto",
    trust_remote_code=True
)
print("模型加载完成！")

def chat_with_model(enable_thinking=True):
    messages = []
    print("\n选择模式：")
    print("1. 开启思考模式")
    print("2. 关闭思考模式")
    print("输入 'quit' 退出对话")
    print("输入 'switch' 切换模式")
    
    # 初始模式选择
    while True:
        mode = input("\n请选择模式 (1/2): ")
        if mode in ['1', '2']:
            enable_thinking = (mode == '1')
            print(f"\n当前模式：{'开启' if enable_thinking else '关闭'}思考模式")
            break
        print("无效输入，请输入 1 或 2")
    
    # 主对话循环
    while True:
        user_input = input("\n你: ")
        if user_input.lower() == 'quit':
            print("\n再见！")
            break
        elif user_input.lower() == 'switch':
            enable_thinking = not enable_thinking
            print(f"\n已切换到：{'开启' if enable_thinking else '关闭'}思考模式")
            continue
            
        messages.append({"role": "user", "content": user_input})
        
        # prepare the model input
        text = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=enable_thinking
        )
        model_inputs = tokenizer([text], return_tensors="pt").to(model.device)

        # 创建流式输出器
        streamer = TextStreamer(tokenizer, skip_special_tokens=True)
        
        print()  # 添加空行
        # 使用streamer进行流式生成
        output_ids = model.generate(
            **model_inputs,
            max_new_tokens=32768,
            streamer=streamer
        )
        
        # 获取生成的完整文本用于保存到对话历史
        output_ids = output_ids[0][len(model_inputs.input_ids[0]):].tolist()
        try:
            # rindex finding 151668 (</think>)
            index = len(output_ids) - output_ids[::-1].index(151668)
        except ValueError:
            index = 0
        
        content = tokenizer.decode(output_ids[index:], skip_special_tokens=True).strip("\n")
        # 将AI的回复添加到对话历史
        messages.append({"role": "assistant", "content": content})
        print()  # 添加空行

if __name__ == "__main__":
    chat_with_model()
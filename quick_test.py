#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试RAG搜索功能
"""

import json
import pandas as pd
import re
from typing import List, Dict, Any

class ShipRAGDatabase:
    """船舶信息RAG数据库"""
    
    def __init__(self):
        self.data = []
        self.df = None
        
    def load_json_files(self, file_paths: List[str]):
        """加载JSON数据文件"""
        print("正在加载数据文件...")
        all_data = []
        
        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    if 'data' in json_data and isinstance(json_data['data'], list):
                        all_data.extend(json_data['data'])
                        print(f"成功加载 {file_path}: {len(json_data['data'])} 条记录")
                    else:
                        print(f"警告: {file_path} 格式不正确")
            except Exception as e:
                print(f"加载 {file_path} 时出错: {e}")
        
        self.data = all_data
        self.df = pd.DataFrame(all_data)
        
        # 数据预处理
        if not self.df.empty:
            # 转换时间格式
            self.df['tgsj'] = pd.to_datetime(self.df['tgsj'], errors='coerce')
            # 填充空值
            self.df['cmch'] = self.df['cmch'].fillna('未知船名')
            self.df['aissbh'] = self.df['aissbh'].fillna('未知AIS')
            
        print(f"数据加载完成！总计 {len(self.data)} 条船舶记录")
        
    def search_ships(self, query: str) -> List[Dict[str, Any]]:
        """搜索船舶信息"""
        if self.df is None or self.df.empty:
            return []
            
        results = []
        
        # 检查是否包含数字（可能是AIS设备号）
        ais_numbers = re.findall(r'\d+', query)
        
        # 检查是否包含日期
        date_matches = re.findall(r'(\d{4}-\d{2}-\d{2})', query)
        
        # 检查是否包含长ID（过闸ID）
        long_id_matches = re.findall(r'[A-F0-9]{20,}', query.upper())
        
        # 提取可能的船名关键词
        ship_name_keywords = []
        for word in query.split():
            if len(word) > 1 and not word.isdigit() and word not in ['查找', '搜索', '船名', '包含', '的', '船舶', 'ais', '设备号', '时间', '日期', '过闸', 'id']:
                ship_name_keywords.append(word)
        
        print(f"搜索分析: AIS号码={ais_numbers}, 日期={date_matches}, 长ID={long_id_matches}, 船名关键词={ship_name_keywords}")
        
        # 1. 按AIS设备号搜索
        if ais_numbers:
            for ais_num in ais_numbers:
                mask = self.df['aissbh'].astype(str).str.contains(ais_num, na=False)
                found = self.df[mask].to_dict('records')
                results.extend(found)
                print(f"AIS {ais_num} 找到 {len(found)} 条记录")
        
        # 2. 按日期搜索
        if date_matches:
            for date_str in date_matches:
                try:
                    target_date = pd.to_datetime(date_str).date()
                    mask = self.df['tgsj'].dt.date == target_date
                    found = self.df[mask].to_dict('records')
                    results.extend(found)
                    print(f"日期 {date_str} 找到 {len(found)} 条记录")
                except:
                    pass
        
        # 3. 按过闸ID搜索
        if long_id_matches:
            for gcdid in long_id_matches:
                mask = self.df['gcdid'].str.contains(gcdid, case=False, na=False)
                found = self.df[mask].to_dict('records')
                results.extend(found)
                print(f"过闸ID {gcdid} 找到 {len(found)} 条记录")
        
        # 4. 按船名关键词搜索
        if ship_name_keywords:
            for keyword in ship_name_keywords:
                mask = self.df['cmch'].str.contains(keyword, case=False, na=False)
                found = self.df[mask].to_dict('records')
                results.extend(found)
                print(f"船名关键词 {keyword} 找到 {len(found)} 条记录")
        
        # 5. 如果没有找到特定模式，进行通用搜索
        if not results:
            search_terms = [word for word in query.split() if len(word) > 1]
            for term in search_terms:
                for col in ['cmch', 'aissbh', 'cbid', 'gcdid']:
                    if col in self.df.columns:
                        mask = self.df[col].astype(str).str.contains(term, case=False, na=False)
                        found = self.df[mask].to_dict('records')
                        results.extend(found)
                        if found:
                            print(f"通用搜索 {term} 在 {col} 找到 {len(found)} 条记录")
        
        # 去重
        seen = set()
        unique_results = []
        for item in results:
            if item['cbid'] not in seen:
                seen.add(item['cbid'])
                unique_results.append(item)
        
        return unique_results[:10]  # 限制返回前10条结果

def format_ship_data(ships: List[Dict[str, Any]]) -> str:
    """格式化船舶数据为可读文本"""
    if not ships:
        return "未找到相关船舶信息。"
    
    formatted_text = f"找到 {len(ships)} 条船舶记录：\n\n"
    for i, ship in enumerate(ships, 1):
        formatted_text += f"{i}. 船舶信息：\n"
        formatted_text += f"   - 船舶ID: {ship.get('cbid', '未知')}\n"
        formatted_text += f"   - 船名: {ship.get('cmch', '未知')}\n"
        formatted_text += f"   - AIS设备号: {ship.get('aissbh', '未知')}\n"
        formatted_text += f"   - 过闸ID: {ship.get('gcdid', '未知')}\n"
        formatted_text += f"   - 通过时间: {ship.get('tgsj', '未知')}\n\n"
    
    return formatted_text

def main():
    # 初始化数据库
    rag_db = ShipRAGDatabase()
    data_files = ["57d55cae476818831e41b8a7edbe3007.json", "dbe6a7821cce479ab6152a446c88b3c4.json"]
    rag_db.load_json_files(data_files)
    
    # 测试搜索
    test_queries = [
        "3957319",
        "江淮货12",
        "2024-09-09",
        "360648E5E42F2ACCCFE22DC261405",
        "浙德清"
    ]
    
    for query in test_queries:
        print(f"\n{'='*50}")
        print(f"搜索查询: {query}")
        print('='*50)
        results = rag_db.search_ships(query)
        print(format_ship_data(results))

if __name__ == "__main__":
    main()
